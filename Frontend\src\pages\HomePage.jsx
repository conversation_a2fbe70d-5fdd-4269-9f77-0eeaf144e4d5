import React from 'react'
import { Link } from '@tanstack/react-router'
import { useSelector } from 'react-redux'
import UrlForm from '../components/UrlForm'

const HomePage = () => {
  const { isAuthenticated } = useSelector((state) => state.auth)

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col items-center justify-center p-4">
      <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
        <h1 className="text-2xl font-bold text-center mb-6">URL Shortener</h1>
        <p className="text-gray-600 text-center mb-6">
          Shorten your long URLs quickly and easily
        </p>
        <UrlForm/>

        {!isAuthenticated && (
          <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <p className="text-sm text-blue-800 text-center mb-3">
              Want to track your URLs and view analytics?
            </p>
            <Link
              to="/auth"
              className="block w-full text-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
            >
              Create Free Account
            </Link>
          </div>
        )}
      </div>
    </div>
  )
}

export default HomePage