import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { getCurrentUser } from '../api/user.api';
import { login } from '../store/slice/authSlice';

const AuthInitializer = ({ children }) => {
  const dispatch = useDispatch();

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const response = await getCurrentUser();
        if (response.success && response.user) {
          dispatch(login(response.user));
        }
      } catch (error) {
        // User is not authenticated, which is fine
        console.log('User not authenticated');
      }
    };

    initializeAuth();
  }, [dispatch]);

  return children;
};

export default AuthInitializer;
