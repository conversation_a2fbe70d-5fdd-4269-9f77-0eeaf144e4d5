import ShortUrl from '../models/shortUrl.model.js';

export const getUserUrls = async (userId) => {
  const urls = await ShortUrl.find({ user: userId })
    .sort({ createdAt: -1 })
    .populate('user', 'name email');
  
  return urls.map(url => ({
    _id: url._id,
    full_url: url.full_url,
    short_url: url.short_url, // Just return the short code, not the full URL
    clicks: url.clicks,
    createdAt: url.createdAt,
  }));
};
