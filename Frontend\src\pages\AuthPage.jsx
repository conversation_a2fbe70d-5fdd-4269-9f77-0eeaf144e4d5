import React, { useState } from 'react'
import LoginForm from '../components/LoginForm'
import RegisterForm from '../components/RegisterForm'

const AuthPage = () => {
    const [isLogin, setIsLogin] = useState(true)

    return (
        <div className="min-h-screen bg-gray-100 flex flex-col items-center justify-center p-4">
            <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
                {/* Tab Headers */}
                <div className="flex mb-6 border-b">
                    <button
                        onClick={() => setIsLogin(true)}
                        className={`flex-1 py-2 px-4 text-center font-medium transition-colors ${
                            isLogin
                                ? 'text-blue-600 border-b-2 border-blue-600'
                                : 'text-gray-500 hover:text-gray-700'
                        }`}
                    >
                        Login
                    </button>
                    <button
                        onClick={() => setIsLogin(false)}
                        className={`flex-1 py-2 px-4 text-center font-medium transition-colors ${
                            !isLogin
                                ? 'text-blue-600 border-b-2 border-blue-600'
                                : 'text-gray-500 hover:text-gray-700'
                        }`}
                    >
                        Register
                    </button>
                </div>

                {/* Form Content */}
                {isLogin ? <LoginForm state={setIsLogin} /> : <RegisterForm state={setIsLogin} />}
            </div>
        </div>
    )
}

export default AuthPage